{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "broken_count": 1, "host": "r2---sn-nx5s7n7d.gvt1.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 7, "host": "www.google-analytics.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 8, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "broken_count": 1, "host": "accounts.youtube.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 4, "host": "signaler-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 27, "broken_until": "**********", "host": "analytics.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 12, "broken_until": "**********", "host": "imagedelivery.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 20, "broken_until": "**********", "host": "www.facebook.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 14, "broken_until": "**********", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 8, "host": "baremetrics-dunning.baremetrics.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "broken_count": 1, "host": "js.stripe.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["IAAAABwAAABodHRwczovL2dvb2dsZXRhZ21hbmFnZXIuY29t", false, 0], "broken_count": 1, "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 12, "broken_until": "1748921115", "host": "googleads.g.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 14, "broken_until": "1748942956", "host": "mp.udio.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "broken_count": 12, "broken_until": "1748937280", "host": "td.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 10, "broken_until": "1748860513", "host": "js.stripe.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 8, "broken_until": "1748844142", "host": "o4506762969219072.ingest.us.sentry.io", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 13, "broken_until": "1748979417", "host": "api.udio.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 12, "broken_until": "1748996514", "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["IAAAABwAAABodHRwczovL2dvb2dsZXRhZ21hbmFnZXIuY29t", false, 0], "broken_count": 10, "host": "google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 11, "broken_until": "1748901872", "host": "connect.facebook.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 2, "host": "google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 9, "broken_until": "1748901908", "host": "js.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 9, "broken_until": "1748901907", "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 7, "broken_until": "1748844309", "host": "newassets.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "broken_count": 7, "broken_until": "1748844308", "host": "newassets.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 9, "broken_until": "1748901910", "host": "dunning.baremetrics.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 8, "broken_until": "**********", "host": "widget.intercom.io", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 5, "host": "a.nel.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 8, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 3, "host": "stats.g.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 1, "host": "lh3.googleusercontent.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "broken_count": 9, "broken_until": "**********", "host": "api.hcaptcha.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "broken_count": 9, "broken_until": "**********", "host": "storage.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "broken_count": 1, "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "www.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395808542406933", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395808543090328", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://dl.google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://consent.cookie-script.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://www.google-analytics.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395809419631968", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL2Nocm9tZS1kZXZ0b29scy1mcm9udGVuZC5hcHBzcG90LmNvbQ==", false, 0], "server": "https://chrome-devtools-frontend.appspot.com", "supports_spdy": true}, {"anonymization": ["IAAAABwAAABodHRwczovL2dvb2dsZXRhZ21hbmFnZXIuY29t", false, 0], "server": "https://google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://a.nel.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393396185177721", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://api.udio.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395901787717976", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://stats.g.doubleclick.net", "supports_spdy": true}, {"anonymization": ["IAAAABwAAABodHRwczovL2dvb2dsZXRhZ21hbmFnZXIuY29t", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://js.stripe.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://o4506762969219072.ingest.us.sentry.io", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://cookie-script.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://cdn.cookie-script.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395829897656899", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://assets.churnkey.co", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://cdn.growthbook.io", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395904528852782", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "server": "https://td.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393398219185914", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://connect.facebook.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393398702849324", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://js.hcaptcha.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395904529229009", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://googleads.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393396185995166", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://newassets.hcaptcha.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395904529772483", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://baremetrics-dunning.baremetrics.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393398930195105", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://widget.intercom.io", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393398930285116", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "server": "https://api.hcaptcha.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393396186031876", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "server": "https://newassets.hcaptcha.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393324299111564", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://js.intercomcdn.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://analytics-ipv6.tiktokw.us", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393398930964942", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://dunning.baremetrics.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "server": "https://js.stripe.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "server": "https://m.stripe.network", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", true, 0], "server": "https://m.stripe.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://api-iam.intercom.io", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395904648109361", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393398266221665", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://imagedelivery.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395904648516570", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393399050903214", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://www.facebook.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://www.udio.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393399068467225", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://mp.udio.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://analytics.tiktok.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395904650841304", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3VkaW8uY29t", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "3G"}}}