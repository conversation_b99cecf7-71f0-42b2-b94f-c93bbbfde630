import asyncio
import json
import os
from playwright.async_api import async_playwright

class PlaywrightSession:
    def __init__(self, cookies_file="cookies.json", url="https://www.udio.com/create"):
        self.cookies_file = cookies_file
        self.url = url
        self.browser = None
        self.context = None
        self.page = None
        self.playwright = None
    
    async def start_browser(self):
        """启动浏览器并创建上下文"""
        self.playwright = await async_playwright().start()
        
        # 使用持久化上下文
        self.context = await self.playwright.chromium.launch_persistent_context(
            user_data_dir="./browser_data",  # 浏览器数据目录
            headless=False,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--no-first-run',
                '--no-default-browser-check'
            ]
        )
        
        # 获取第一个页面或创建新页面
        if len(self.context.pages) > 0:
            self.page = self.context.pages[0]
        else:
            self.page = await self.context.new_page()
        
        # 打开指定网页
        await self.page.goto(self.url)
        print(f"已打开网页: {self.url}")
    
    async def load_cookies(self):
        """从文件加载cookies"""
        try:
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            await self.context.add_cookies(cookies)
        except Exception as e:
            print(f"加载cookies失败: {e}")
    
    async def save_cookies(self):
        """保存cookies到文件"""
        try:
            cookies = await self.context.cookies()
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            print(f"cookies已保存到: {self.cookies_file}")
        except Exception as e:
            print(f"保存cookies失败: {e}")
    
    async def wait_for_user_action(self):
        """等待用户操作（登录等）"""
        print("\n请在浏览器中完成登录操作...")
        print("完成后请在此终端按 Enter 键继续...")
        input()  # 等待用户按Enter
    
    async def close(self):
        """关闭浏览器并保存cookies"""
        try:
            if self.context:
                await self.save_cookies()
                await self.context.close()
                print("浏览器上下文已关闭")
            
            if self.playwright:
                await self.playwright.stop()
                print("Playwright已停止")
                
        except Exception as e:
            print(f"关闭时发生错误: {e}")
        finally:
            print("cookies已保存")

async def main():
    # 创建会话实例
    session = PlaywrightSession(
        cookies_file="login_cookies.json",
        url="https://www.udio.com/create"
    )
    
    try:
        # 启动浏览器
        await session.start_browser()
        
        # 等待用户登录
        await session.wait_for_user_action()
        
        # 关闭浏览器并保存cookies
        await session.close()
        
    except Exception as e:
        print(f"发生错误: {e}")
        # 确保清理资源
        try:
            if session.context:
                await session.context.close()
            if session.playwright:
                await session.playwright.stop()
        except:
            pass

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())