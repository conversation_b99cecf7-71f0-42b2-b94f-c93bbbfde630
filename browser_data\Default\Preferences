{"accessibility": {"captions": {"live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1011, "left": 616, "maximized": false, "right": 1912, "top": 156, "work_area_bottom": 1112, "work_area_left": 0, "work_area_right": 2048, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements-panel-split-view-state": "{\"vertical\":{\"size\":97}}", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":949.4000244140625}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "releaseNoteVersionSeen": "77", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "cebb5b0e-7576-4399-8c13-f5edba07c23a", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.7103.25"}, "gaia_cookie": {"changed_time": **********.067214, "hash": "voEAznZh18ZuAjuQ2/6KfXGc0TU=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"wen xue\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-BA_sdq2ysYU/AAAAAAAAAAI/AAAAAAAAAAA/__z4ExZnNE8/s48-c/photo.jpg\",1,1,0,null,1,\"106836389524664463208\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.windows"}, "google": {"services": {"signin_scoped_device_id": "fcc3e4fe-edb6-4f76-be9f-4777da9632f5"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"device_id_salt": "FEC47066DA4E82F9E9B5158F5B4CB5B7", "engagement": {"schema_version": 5}}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]udio.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": true}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.udio.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 46}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3393222070605912e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.7, "rawScore": 2.7}}, "https://www.udio.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3393309947672692e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 29.***************}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.25", "creation_time": "13393216541376617", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13393309947672692", "last_time_obsolete_http_credentials_removed": 1748743001.415656, "last_time_password_store_metrics_reported": 1748830559.791702, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chromium", "password_hash_data_list": [{"hash": "djEwBQov4P5dR1eRFIJ0d+PtKFGrTqbYSnwovybIR5L9qsAU/4p2Yy9WbQ==", "is_gaia": "djEw1lh+pUNR8vep7LGSkLBrcNOK5R6OzyMYBzSqCKQzvV8=", "last_signin": 1748743001.329261, "salt_length": "djEwhJzLToRF0aA+jaOzmDYXRLDqRsM3wpXH3OKgrzwYTtB5KWI2Kw+c/GqmquMcfEo=", "username": "djEwbmWRO91ejb6yFeg/ViBJRGITAwrAd1nyTNGX9Reb0dcjbaalDTwoVDDWujU="}], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13393304129", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EOGE2fq5pOUXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEM2F2fq5pOUXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQorm71fOh5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADENW5u9XzoeUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13393209599000000", "uma_in_sql_start_time": "13393216541410062"}, "selectfile": {"last_directory": "C:\\Users\\<USER>\\Documents\\AudFree Apple Music Converter\\Output\\pure focus"}, "sessions": {"event_log": [{"crashed": false, "time": "13393233601689266", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393237884925748", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393237893253890", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393238324859845", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393238332269350", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393238769732233", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393238775696910", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393238858129167", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393239099433378", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393241083953471", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393241109917580", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393257460862855", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393257862138943", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393300087952744", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393304129785330", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393304271790732", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393304577647198", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393304660633281", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393309782690009", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136"}}