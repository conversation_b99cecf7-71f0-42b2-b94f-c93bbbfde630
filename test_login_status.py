import asyncio
import json
import os
from playwright.async_api import async_playwright

class LoginTester:
    def __init__(self, cookies_file="login_cookies.json", url="https://www.udio.com/create"):
        self.cookies_file = cookies_file
        self.url = url
        self.context = None
        self.page = None
        self.playwright = None
    
    async def start_browser_with_cookies(self):
        """启动浏览器并加载保存的cookies"""
        self.playwright = await async_playwright().start()
        
        # 使用持久化上下文（会自动加载之前保存的数据）
        self.context = await self.playwright.chromium.launch_persistent_context(
            user_data_dir="./browser_data",  # 使用相同的浏览器数据目录
            headless=False,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--no-first-run',
                '--no-default-browser-check'
            ]
        )
        
        # 获取第一个页面或创建新页面
        if len(self.context.pages) > 0:
            self.page = self.context.pages[0]
        else:
            self.page = await self.context.new_page()
        
        print(f"正在打开网页: {self.url}")
        await self.page.goto(self.url)
        print("网页已打开，请检查是否已自动登录")
        
        # 等待页面基本加载完成，但不等待网络空闲（避免超时）
        try:
            await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
            print("页面DOM加载完成")
        except Exception as e:
            print(f"等待页面加载时出现警告: {e}")
        
        return self.page
    
    async def check_login_status(self):
        """检查登录状态"""
        try:
            # 等待几秒让页面完全加载
            print("等待页面加载...")
            await asyncio.sleep(5)
            
            # 获取当前页面的标题和URL
            title = await self.page.title()
            current_url = self.page.url
            
            print(f"当前页面标题: {title}")
            print(f"当前页面URL: {current_url}")
            
            # 检查页面内容来判断登录状态
            try:
                # 检查页面文本内容
                page_content = await self.page.content()
                
                # 常见的登录状态指示器
                if any(keyword in page_content.lower() for keyword in ['sign in', 'login', '登录', 'log in']):
                    print("🔍 页面中发现登录相关文本，可能需要登录")
                
                if any(keyword in page_content.lower() for keyword in ['logout', 'sign out', '退出', 'log out', 'profile', '个人资料']):
                    print("✅ 页面中发现已登录用户相关文本，可能已经登录")
                
                # 检查是否被重定向到登录页面
                if 'login' in current_url.lower() or 'signin' in current_url.lower():
                    print("❌ 当前URL包含登录页面标识，可能需要重新登录")
                else:
                    print("✅ 当前URL看起来不是登录页面")
                    
            except Exception as e:
                print(f"检查页面内容时发生错误: {e}")
                
        except Exception as e:
            print(f"检查登录状态时发生错误: {e}")
    
    async def wait_and_close(self):
        """等待用户检查后关闭"""
        print("\n=== 登录状态检查完成 ===")
        print("请在浏览器中手动检查登录状态...")
        print("- 如果已经登录，说明cookies保存和加载成功")
        print("- 如果需要重新登录，可能是cookies过期或网站更新了验证机制")
        print("\n检查完成后请在此终端按 Enter 键关闭浏览器...")
        input()
        
        try:
            if self.context:
                await self.context.close()
                print("浏览器上下文已关闭")
            
            if self.playwright:
                await self.playwright.stop()
                print("Playwright已停止")
                
        except Exception as e:
            print(f"关闭时发生错误: {e}")

async def main():
    # 检查cookies文件是否存在
    if not os.path.exists("login_cookies.json"):
        print("❌ 未找到 login_cookies.json 文件")
        print("请先运行 playwright_login.py 进行登录")
        return
    
    print("✅ 找到 login_cookies.json 文件")
    
    # 检查browser_data目录
    if os.path.exists("./browser_data"):
        print("✅ 找到 browser_data 目录，将使用保存的浏览器数据")
    else:
        print("⚠️ 未找到 browser_data 目录")
    
    print("正在测试登录状态...")
    
    tester = LoginTester()
    
    try:
        # 启动浏览器并加载cookies
        await tester.start_browser_with_cookies()
        
        # 检查登录状态
        await tester.check_login_status()
        
        # 等待用户检查
        await tester.wait_and_close()
        
    except Exception as e:
        print(f"发生错误: {e}")
        try:
            if tester.context:
                await tester.context.close()
            if tester.playwright:
                await tester.playwright.stop()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(main())