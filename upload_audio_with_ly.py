import asyncio
import os
import json
import glob
import asyncio
import traceback
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

class AudioUploader:
    def __init__(self, url="https://www.udio.com/create"):
        self.url = url
        self.context = None
        self.page = None
        self.playwright = None
    
    async def start_browser(self):
        """启动浏览器并加载保存的登录状态"""
        self.playwright = await async_playwright().start()
        
        # 使用持久化上下文（会自动加载之前保存的登录状态）
        self.context = await self.playwright.chromium.launch_persistent_context(
            user_data_dir="./browser_data",  # 使用相同的浏览器数据目录
            headless=False,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--no-first-run',
                '--no-default-browser-check'
            ]
        )
        
        # 获取第一个页面或创建新页面
        if len(self.context.pages) > 0:
            self.page = self.context.pages[0]
        else:
            self.page = await self.context.new_page()
        
        print(f"正在打开网页: {self.url}")
        await self.page.goto(self.url)
        print("网页已打开")
        
        # 等待页面基本加载完成
        try:
            await self.page.wait_for_load_state('domcontentloaded', timeout=15000)
            print("页面DOM加载完成")
        except Exception as e:
            print(f"等待页面加载时出现警告: {e}")
        
        return self.page
    
    async def upload_audio_file(self, audio_file_path):
        """点击Upload Audio按钮并上传音频文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_file_path):
                print(f"❌ 文件不存在: {audio_file_path}")
                return False
            
            print(f"准备上传文件: {audio_file_path}")
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            # 寻找title为"Upload Audio"的按钮
            upload_selectors = [
                '[title="Upload Audio"]',
                'button[title="Upload Audio"]',
                '[aria-label="Upload Audio"]',
                'button[aria-label="Upload Audio"]',
                'button:has-text("Upload Audio")',
                '.upload-button',
                '[data-testid*="upload"]'
            ]
            
            upload_button = None
            for selector in upload_selectors:
                try:
                    upload_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if upload_button:
                        print(f"✅ 找到Upload Audio按钮: {selector}")
                        break
                except:
                    continue
            
            if not upload_button:
                print("❌ 未找到Upload Audio按钮")
                return False
            
            # 设置文件选择器监听
            async def handle_file_chooser(file_chooser):
                print(f"📁 文件选择器已打开，正在选择文件: {audio_file_path}")
                await file_chooser.set_files(audio_file_path)
                print("✅ 文件已选择")
            
            # 监听文件选择器事件
            self.page.on("filechooser", handle_file_chooser)
            
            # 点击Upload Audio按钮
            print("🖱️ 点击Upload Audio按钮...")
            await upload_button.click()
            
            # 等待文件上传完成
            print("⏳ 等待文件上传...")
            await asyncio.sleep(5)
            
            # 移除文件选择器监听
            self.page.remove_listener("filechooser", handle_file_chooser)
            
            print("✅ 文件上传操作完成")
            return True
            
        except Exception as e:
            print(f"❌ 上传文件时发生错误: {e}")
            return False
    
    async def handle_understand_button_and_style(self):
        """处理I understand按钮、Style元素、Instrumental标签和Add to Folder操作"""
        try:
            print("\n=== 处理I understand按钮 ===")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 寻找aria-label为"I understand"的按钮
            understand_button = None
            try:
                understand_button = await self.page.wait_for_selector('button[aria-label="I understand"]', timeout=5000)
                print("✅ 找到I understand按钮")
            except Exception:
                print("❌ 未找到I understand按钮")
                # 尝试其他可能的选择器
                alternative_selectors = [
                    '[aria-label="I understand"]',
                    'button:has-text("I understand")',
                    '[role="button"][aria-label="I understand"]'
                ]
                
                for selector in alternative_selectors:
                    try:
                        understand_button = await self.page.wait_for_selector(selector, timeout=3000)
                        if understand_button:
                            print(f"✅ 通过备用选择器找到I understand按钮: {selector}")
                            break
                    except Exception:
                        continue
            
            if understand_button:
                # 检查aria-checked属性
                aria_checked = await understand_button.get_attribute("aria-checked")
                print(f"I understand按钮的aria-checked状态: {aria_checked}")
                
                if aria_checked == "false" or aria_checked is None:
                    print("正在点击I understand按钮...")
                    await understand_button.click()
                    print("✅ 已点击I understand按钮")
                    
                    # 等待状态更新
                    await asyncio.sleep(1)
                    
                    # 验证状态是否已更改
                    new_aria_checked = await understand_button.get_attribute("aria-checked")
                    print(f"点击后的aria-checked状态: {new_aria_checked}")
                else:
                    print("I understand按钮已经是选中状态，无需点击")
            else:
                print("⚠️ 未找到I understand按钮，可能页面结构不同")
            
            print("\n=== 处理Style元素 ===")
            
            # 等待一下确保页面更新
            await asyncio.sleep(2)
            
            # 寻找Style元素
            style_element = None
            style_selectors = [
                'span.cursor-pointer:has-text("Style")',
                'span[class*="cursor-pointer"]:has-text("Style")',
                'span:has-text("Style").cursor-pointer',
                '.cursor-pointer:has-text("Style")',
                'span:has-text("Style")',
                '[class*="cursor-pointer"]:has-text("Style")'
            ]
            
            for selector in style_selectors:
                try:
                    style_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if style_element:
                        print(f"✅ 找到Style元素: {selector}")
                        break
                except Exception:
                    continue
            
            if style_element:
                print("正在点击Style元素...")
                await style_element.click()
                print("✅ 已点击Style元素")
                
                # 等待页面响应
                await asyncio.sleep(2)
            else:
                print("❌ 未找到Style元素")
                
                # 打印页面中所有包含"Style"文本的元素
                try:
                    style_elements = await self.page.query_selector_all('*:has-text("Style")')
                    print(f"页面中找到 {len(style_elements)} 个包含'Style'的元素")
                    
                    for i, elem in enumerate(style_elements[:5]):  # 只显示前5个
                        tag_name = await elem.evaluate('el => el.tagName')
                        class_name = await elem.get_attribute('class')
                        text_content = await elem.inner_text()
                        print(f"Style元素 {i}: <{tag_name}> class='{class_name}' text='{text_content[:50]}'")
                        
                except Exception as e:
                    print(f"查找Style元素时发生错误: {e}")
            
            print("\n=== 处理Instrumental标签 ===")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 寻找Instrumental标签
            instrumental_element = None
            instrumental_selectors = [
                'label[for="instrumental"]',
                'label:has-text("Instrumental")',
                '[class*="cursor-pointer"]:has-text("Instrumental")',
                'label[for="instrumental"][class*="cursor-pointer"]',
                'label[for="instrumental"][class*="flex"]',
                '*:has-text("Instrumental")[class*="cursor-pointer"]'
            ]
            
            for selector in instrumental_selectors:
                try:
                    instrumental_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if instrumental_element:
                        print(f"✅ 找到Instrumental标签: {selector}")
                        break
                except Exception:
                    continue
            
            if instrumental_element:
                print("正在点击Instrumental标签...")
                await instrumental_element.click()
                print("✅ 已点击Instrumental标签")
                
                # 等待页面响应
                await asyncio.sleep(2)
            else:
                print("❌ 未找到Instrumental标签")
                
                # 打印页面中所有包含"Instrumental"文本的元素
                try:
                    instrumental_elements = await self.page.query_selector_all('*:has-text("Instrumental")')
                    print(f"页面中找到 {len(instrumental_elements)} 个包含'Instrumental'的元素")
                    
                    for i, elem in enumerate(instrumental_elements[:5]):  # 只显示前5个
                        tag_name = await elem.evaluate('el => el.tagName')
                        class_name = await elem.get_attribute('class')
                        for_attr = await elem.get_attribute('for')
                        text_content = await elem.inner_text()
                        print(f"Instrumental元素 {i}: <{tag_name}> class='{class_name}' for='{for_attr}' text='{text_content[:50]}'")
                        
                except Exception as e:
                    print(f"查找Instrumental元素时发生错误: {e}")
            
            print("\n=== 处理Style similarity slider ===")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 寻找Style similarity slider
            slider_element = None
            slider_selectors = [
                'span[title="Style similarity slider"] input[type="range"]',
                'input[type="range"][aria-valuemax="5"]',
                'input[type="range"][min="1"][max="5"]',
                '.MuiSlider-root input[type="range"]',
                'span[title="Style similarity slider"] .MuiSlider-thumb input',
                'input[aria-valuenow][aria-valuemax="5"]'
            ]
            
            for selector in slider_selectors:
                try:
                    slider_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if slider_element:
                        print(f"✅ 找到Style similarity slider: {selector}")
                        break
                except Exception:
                    continue
            
            if slider_element:
                # 获取当前滑块值
                current_value = await slider_element.get_attribute("value")
                aria_valuenow = await slider_element.get_attribute("aria-valuenow")
                min_value = await slider_element.get_attribute("min") or "1"
                max_value = await slider_element.get_attribute("max") or "5"
                print(f"当前滑块值: value={current_value}, aria-valuenow={aria_valuenow}, min={min_value}, max={max_value}")
                
                # 方法1: 更精确的JavaScript操作
                try:
                    print("正在将滑块拖动到最右边 (值=5)...")
                    
                    # 获取滑块的当前属性
                    min_val = await slider_element.get_attribute("min") or "1"
                    max_val = await slider_element.get_attribute("max") or "5"
                    
                    # 使用更强力的JavaScript操作
                    result = await slider_element.evaluate(f'''
                        (el) => {{
                            const minValue = parseFloat("{min_val}");
                            const maxValue = parseFloat("{max_val}");
                            
                            console.log('开始设置滑块值:', maxValue);
                            
                            // 找到所有可能的父容器
                            const slider = el;
                            const sliderRoot = el.closest('.MuiSlider-root, [class*="slider"], [class*="Slider"], [role="slider"]');
                            const sliderContainer = el.closest('[title="Style similarity slider"]');
                            
                            console.log('滑块元素:', slider);
                            console.log('滑块根容器:', sliderRoot);
                            console.log('滑块容器:', sliderContainer);
                            
                            // 方法1: 直接设置并触发原生事件
                            slider.value = maxValue.toString();
                            slider.setAttribute("aria-valuenow", maxValue.toString());
                            
                            // 方法2: 使用React的方式设置值
                            if (slider._valueTracker) {{
                                slider._valueTracker.setValue(maxValue.toString());
                            }}
                            
                            // 方法3: 触发React的onChange事件
                            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                            if (nativeInputValueSetter) {{
                                nativeInputValueSetter.call(slider, maxValue.toString());
                            }}
                            
                            // 方法4: 创建并触发各种事件
                            const events = [
                                new Event('input', {{ bubbles: true, cancelable: true }}),
                                new Event('change', {{ bubbles: true, cancelable: true }}),
                                new MouseEvent('mousedown', {{ bubbles: true }}),
                                new MouseEvent('mousemove', {{ bubbles: true }}),
                                new MouseEvent('mouseup', {{ bubbles: true }}),
                                new Event('blur', {{ bubbles: true }}),
                                new Event('focus', {{ bubbles: true }})
                            ];
                            
                            events.forEach(event => {{
                                console.log('触发事件:', event.type);
                                slider.dispatchEvent(event);
                            }});
                            
                            // 方法5: 针对Material-UI的特殊处理
                            if (sliderRoot) {{
                                console.log('处理Material-UI滑块');
                                
                                // 触发Material-UI的onChange
                                const muiEvents = [
                                    new CustomEvent('change', {{
                                        detail: {{ value: maxValue }},
                                        bubbles: true
                                    }}),
                                    new CustomEvent('input', {{
                                        detail: {{ value: maxValue }},
                                        bubbles: true
                                    }})
                                ];
                                
                                muiEvents.forEach(event => {{
                                    sliderRoot.dispatchEvent(event);
                                    slider.dispatchEvent(event);
                                }});
                                
                                // 尝试直接调用React的事件处理器
                                const reactKey = Object.keys(slider).find(key => key.startsWith('__reactInternalInstance') || key.startsWith('__reactFiber'));
                                if (reactKey && slider[reactKey]) {{
                                    console.log('找到React实例，尝试触发React事件');
                                    const reactInstance = slider[reactKey];
                                    if (reactInstance.memoizedProps && reactInstance.memoizedProps.onChange) {{
                                        try {{
                                            reactInstance.memoizedProps.onChange({{ target: {{ value: maxValue.toString() }} }});
                                        }} catch (e) {{
                                            console.log('React onChange调用失败:', e);
                                        }}
                                    }}
                                }}
                            }}
                            
                            // 方法6: 强制更新DOM样式
                            const percentage = ((maxValue - minValue) / (maxValue - minValue)) * 100;
                            if (sliderRoot) {{
                                const thumb = sliderRoot.querySelector('.MuiSlider-thumb, [class*="thumb"], [class*="Thumb"]');
                                const track = sliderRoot.querySelector('.MuiSlider-track, [class*="track"], [class*="Track"]');
                                
                                if (thumb) {{
                                    thumb.style.left = '100%';
                                    thumb.style.transform = 'translateX(-100%)';
                                }}
                                
                                if (track) {{
                                    track.style.width = '100%';
                                }}
                            }}
                            
                            // 等待一下让事件处理完成
                            await new Promise(resolve => setTimeout(resolve, 100));
                            
                            return {{
                                value: slider.value,
                                ariaNow: slider.getAttribute('aria-valuenow'),
                                min: minValue,
                                max: maxValue,
                                hasReact: !!Object.keys(slider).find(key => key.startsWith('__react')),
                                sliderRootFound: !!sliderRoot
                            }};
                        }}
                    ''')
                    
                    print(f"强化JavaScript操作结果: {result}")
                    await asyncio.sleep(2)  # 等待更长时间让UI更新
                    
                except Exception as e1:
                    print(f"强化方法失败: {e1}")
                    
                    # 备用方法: 物理拖拽模拟
                    try:
                        print("尝试备用方法: 物理拖拽模拟...")
                        
                        # 获取滑块容器
                        slider_container = await self.page.query_selector('span[title="Style similarity slider"]')
                        if slider_container:
                            container_box = await slider_container.bounding_box()
                            if container_box:
                                # 计算滑块轨道的位置
                                track_left = container_box['x'] + 20  # 留一些边距
                                track_right = container_box['x'] + container_box['width'] - 20
                                track_y = container_box['y'] + container_box['height'] / 2
                                
                                print(f"滑块轨道: left={track_left}, right={track_right}, y={track_y}")
                                
                                # 先点击滑块区域
                                await self.page.mouse.click(track_right, track_y)
                                await asyncio.sleep(0.5)
                                
                                # 执行拖拽到最右边
                                await self.page.mouse.move(track_left, track_y)
                                await self.page.mouse.down()
                                await asyncio.sleep(0.1)
                                
                                # 缓慢拖动到最右边
                                steps = 20
                                for i in range(1, steps + 1):
                                    x = track_left + (track_right - track_left) * i / steps
                                    await self.page.mouse.move(x, track_y)
                                    await asyncio.sleep(0.05)
                                
                                await self.page.mouse.up()
                                await asyncio.sleep(1)
                                
                                print("✅ 物理拖拽完成")
                        
                    except Exception as e2:
                        print(f"备用方法也失败: {e2}")
                        
                        # 方法3: 键盘操作确保到最右边
                        try:
                            print("尝试方法3: 键盘操作...")
                            
                            # 聚焦到滑块
                            await slider_element.focus()
                            await asyncio.sleep(0.2)
                            
                            # 先按End键直接到最右边
                            await self.page.keyboard.press('End')
                            await asyncio.sleep(0.3)
                            
                            # 验证是否到了最右边，如果没有就用箭头键
                            current_val = await slider_element.get_attribute("value")
                            max_val = await slider_element.get_attribute("max") or "5"
                            
                            if current_val != max_val:
                                print(f"End键后值为{current_val}，继续用右箭头键")
                                # 多次按右箭头确保到最右边
                                for i in range(10):
                                    await self.page.keyboard.press('ArrowRight')
                                    await asyncio.sleep(0.05)
                                    
                                    # 检查是否已经到最大值
                                    check_val = await slider_element.get_attribute("value")
                                    if check_val == max_val:
                                        print(f"第{i+1}次右箭头后达到最大值")
                                        break
                            
                            print("✅ 键盘操作完成")
                            
                        except Exception as e3:
                            print(f"方法3也失败: {e3}")
                
                # 等待页面响应
                await asyncio.sleep(2)
                
                # 验证滑块值是否已更改
                try:
                    new_value = await slider_element.get_attribute("value")
                    new_aria_valuenow = await slider_element.get_attribute("aria-valuenow")
                    print(f"操作后滑块值: value={new_value}, aria-valuenow={new_aria_valuenow}")
                    
                    # 检查是否成功设置为最大值
                    if new_value == max_value or new_aria_valuenow == max_value:
                        print("✅ 滑块已成功设置为最大值")
                    else:
                        print(f"⚠️ 滑块值可能未完全设置为最大值，当前值: {new_value or new_aria_valuenow}")
                        
                        # 最后尝试：直接模拟用户交互
                        try:
                            print("最后尝试：模拟完整用户交互...")
                            await slider_element.click()
                            await asyncio.sleep(0.2)
                            
                            # 使用JavaScript直接设置并触发React/Vue事件
                            await slider_element.evaluate(f'''
                                el => {{
                                    const maxVal = "{max_value}";
                                    el.value = maxVal;
                                    el.setAttribute("aria-valuenow", maxVal);
                                    
                                    // 触发React onChange
                                    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                                    nativeInputValueSetter.call(el, maxVal);
                                    
                                    const event = new Event('input', {{ bubbles: true }});
                                    el.dispatchEvent(event);
                                }}
                            ''')
                            
                            await asyncio.sleep(1)
                            final_value = await slider_element.get_attribute("value")
                            print(f"最终滑块值: {final_value}")
                            
                        except Exception as e4:
                            print(f"最后尝试也失败: {e4}")
                    
                except Exception:
                    pass
                
                # 处理完滑块后点击Advanced Controls按钮
                await self.click_advanced_controls()
                
                return True
            else:
                print("❌ 未找到Style similarity slider")
                
                # 打印页面中所有滑块相关元素
                try:
                    slider_elements = await self.page.query_selector_all('input[type="range"], .MuiSlider-root, [title*="slider"]')
                    print(f"页面中找到 {len(slider_elements)} 个滑块相关元素")
                    
                    for i, elem in enumerate(slider_elements[:5]):  # 只显示前5个
                        tag_name = await elem.evaluate('el => el.tagName')
                        class_name = await elem.get_attribute('class')
                        title_attr = await elem.get_attribute('title')
                        type_attr = await elem.get_attribute('type')
                        print(f"滑块元素 {i}: <{tag_name}> class='{class_name}' title='{title_attr}' type='{type_attr}'")
                        
                except Exception as e:
                    print(f"查找滑块元素时发生错误: {e}")
                
                return False
                
        except Exception as e:
            print(f"处理所有元素时发生错误: {e}")
            return False
    
    # 旧版本的click_create_button方法已移除
            
    async def click_reset_button(self):
        try:
            print("=== 点击Reset按钮 ===")
            
            # 多种选择器尝试定位Reset按钮
            reset_selectors = [
                'button:has-text("Reset")',
                'button[title="Reset Create"]',
                'button.whitespace-nowrap:has-text("Reset")',
                'button.bg-gray-panel:has-text("Reset")',
                'button:has(svg):has-text("Reset")',
                'button.justify-center:has(svg.lucide-undo2)'
            ]
            
            reset_button = None
            for selector in reset_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        reset_button = button
                        print(f"✅ 找到Reset按钮: {selector}")
                        break
                except Exception as e:
                    print(f"尝试选择器 {selector} 失败: {e}")
            
            if not reset_button:
                # 尝试JavaScript查找复杂按钮结构
                reset_button_js = """
                    () => {
                        const resetBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                            btn.innerText.includes('Reset') || 
                            (btn.title && btn.title.includes('Reset')) ||
                            btn.querySelector('svg.lucide-undo2')
                        );
                        if (resetBtn) {
                            const rect = resetBtn.getBoundingClientRect();
                            return {
                                found: true,
                                x: rect.x + rect.width/2,
                                y: rect.y + rect.height/2
                            };
                        }
                        return { found: false };
                    }
                """
                reset_btn_result = await self.page.evaluate(reset_button_js)
                
                if reset_btn_result.get('found', False):
                    print("✅ 通过JavaScript找到Reset按钮")
                    # 使用鼠标点击坐标
                    await self.page.mouse.click(reset_btn_result['x'], reset_btn_result['y'])
                    print("✅ 通过坐标点击了Reset按钮")
                    await asyncio.sleep(2)  # 等待重置完成
                    return True
                else:
                    print("⚠️ 无法找到Reset按钮，可能需要手动操作")
                    return False
            
            print("🖱️ 点击Reset按钮...")
            await reset_button.click()
            await asyncio.sleep(2)  # 等待重置完成
            
            print("✅ Reset按钮点击操作完成")
            return True
            
        except Exception as e:
            print(f"点击Reset按钮时出错: {e}")
            traceback.print_exc()
            return False
    
    async def click_create_button(self):
        try:
            print("=== 点击Create按钮 ===")
            
            # 多种选择器尝试定位Create按钮
            button_selectors = [
                'button:has-text("Create")',
                'button.justify-center:has-text("Create")',
                'button[type="submit"]:has-text("Create")',
                'button.whitespace-nowrap:has-text("Create")',
                'button.bg-primary:has-text("Create")'
            ]
            
            create_button = None
            for selector in button_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        create_button = button
                        print(f"✅ 找到Create按钮: {selector}")
                        break
                except Exception as e:
                    print(f"尝试选择器 {selector} 失败: {e}")
            
            if not create_button:
                # 尝试使用aria属性或其他属性
                create_button = await self.page.query_selector('button[aria-label="Create"], button[title="Create"]')
                
            if not create_button:
                print("⚠️ 无法找到Create按钮")
                return False
            
            # 检查按钮是否禁用
            is_disabled = await create_button.get_attribute("disabled")
            if is_disabled:
                print("⚠️ Create按钮当前处于禁用状态，等待其变为可用...")
                # 等待按钮变为可用
                await self.page.wait_for_selector('button:has-text("Create"):not([disabled])', timeout=30000)
                # 重新获取可用的按钮
                create_button = await self.page.query_selector('button:has-text("Create"):not([disabled])')
                if not create_button:
                    print("⚠️ 等待Create按钮变为可用超时")
                    return False
            
            print("🖱️ 点击Create按钮...")
            await create_button.click()
            
            # 验证点击是否成功 - 检查是否有进度指示器或其他确认
            try:
                progress_indicator = await self.page.wait_for_selector(
                    'div[role="progressbar"], .progress-bar, .loading-indicator',
                    timeout=5000
                )
                if progress_indicator:
                    print("✅ 检测到进度指示器，生成过程已开始")
            except Exception:
                print("⚠️ 未检测到进度指示器，但操作可能仍然成功")
            
            print("✅ Create按钮点击操作完成")
            return True
            
        except Exception as e:
            print(f"点击Create按钮时出错: {e}")
            traceback.print_exc()
            return False
    
    async def adjust_generation_quality_slider(self):
        """将Generation quality slider滑块拖动到最右边的Ultra位置"""
        try:
            print("\n=== 处理Generation quality slider ===")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 寻找Generation quality slider
            quality_slider_element = None
            quality_slider_selectors = [
                'span[title="Generation quality slider"] input[type="range"]',
                'input[aria-orientation="horizontal"][type="range"]',
                '.MuiSlider-root input[type="range"]',
                'input[min="0.25"][max="1"][type="range"]',
                'input[aria-valuemin="0.25"][aria-valuemax="1"]',
                '.MuiSlider-thumb input'
            ]
            
            for selector in quality_slider_selectors:
                try:
                    quality_slider_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if quality_slider_element:
                        print(f"✅ 找到Generation quality slider: {selector}")
                        break
                except Exception:
                    continue
            
            if quality_slider_element:
                # 获取当前滑块值
                current_value = await quality_slider_element.get_attribute("value")
                aria_valuenow = await quality_slider_element.get_attribute("aria-valuenow")
                min_value = await quality_slider_element.get_attribute("min") or "0.25"
                max_value = await quality_slider_element.get_attribute("max") or "1"
                print(f"当前滑块值: value={current_value}, aria-valuenow={aria_valuenow}, min={min_value}, max={max_value}")
                
                print(f"将滑块从{current_value or aria_valuenow}设置到最大值{max_value} (Ultra位置)")
                
                # 方法1: 强化JavaScript操作
                try:
                    print("正在使用JavaScript设置滑块到Ultra位置...")
                    
                    # 执行复杂的JavaScript来精确调整滑块
                    result = await quality_slider_element.evaluate(f'''
                        el => {{
                            const maxValue = parseFloat("{max_value}");
                            console.log('开始设置Generation quality滑块值到', maxValue);
                            
                            // 直接设置值
                            el.value = maxValue.toString();
                            el.setAttribute("aria-valuenow", maxValue.toString());
                            
                            // 触发事件
                            const events = [
                                new Event('input', {{ bubbles: true }}),
                                new Event('change', {{ bubbles: true }})
                            ];
                            
                            events.forEach(event => el.dispatchEvent(event));
                            
                            // 找到滑块的父容器
                            const sliderRoot = el.closest('span[title="Generation quality slider"]') || 
                                               el.closest('.MuiSlider-root') || 
                                               el.closest('[class*="slider"]');
                            
                            if (sliderRoot) {{
                                console.log('找到滑块父容器:', sliderRoot);
                                
                                // 强制更新滑块的外观
                                // 找到拉手并更新其位置
                                const thumb = sliderRoot.querySelector('.MuiSlider-thumb') || 
                                               sliderRoot.querySelector('[class*="thumb"]');
                                if (thumb) {{
                                    thumb.style.left = '100%';
                                }}
                                
                                // 更新轨道
                                const track = sliderRoot.querySelector('.MuiSlider-track') || 
                                               sliderRoot.querySelector('[class*="track"]');
                                if (track) {{
                                    track.style.width = '100%';
                                    track.style.left = '0%';
                                }}
                                
                                // 更新mark标记
                                const marks = sliderRoot.querySelectorAll('.MuiSlider-mark');
                                marks.forEach(mark => {{
                                    mark.classList.add('MuiSlider-markActive');
                                }});
                                
                                // 更新标签
                                const markLabels = sliderRoot.querySelectorAll('.MuiSlider-markLabel');
                                markLabels.forEach(label => {{
                                    label.classList.add('MuiSlider-markLabelActive');
                                }});
                                
                                // 将Ultra标签设置为激活状态
                                const ultraLabel = Array.from(markLabels).find(label => label.textContent === 'Ultra');
                                if (ultraLabel) {{
                                    ultraLabel.classList.add('MuiSlider-markLabelActive');
                                    // 移除其他标签的激活状态
                                    markLabels.forEach(label => {{
                                        if (label !== ultraLabel) {{
                                            label.classList.remove('MuiSlider-markLabelActive');
                                        }}
                                    }});
                                }}
                            }}
                            
                            return {{
                                newValue: el.value,
                                newAriaNow: el.getAttribute('aria-valuenow'),
                                sliderRootFound: !!sliderRoot
                            }};
                        }}
                    ''');
                    
                    print(f"JavaScript操作结果: {result}")
                    await asyncio.sleep(1)  # 等待UI更新
                    
                except Exception as e1:
                    print(f"JavaScript方法失败: {e1}")
                    
                    # 备用方法: 使用模拟物理拖拽
                    try:
                        print("尝试备用方法: 物理拖拽模拟...")
                        
                        # 找到滑块的父容器
                        slider_container = await self.page.query_selector('span[title="Generation quality slider"]') or \
                                           await self.page.query_selector('.MuiSlider-root')
                        
                        if slider_container:
                            container_box = await slider_container.bounding_box()
                            if container_box:
                                # 计算滑块轨道的位置
                                track_left = container_box['x'] + 20  # 留一些边距
                                track_right = container_box['x'] + container_box['width'] - 20
                                track_y = container_box['y'] + container_box['height'] / 2
                                
                                print(f"滑块轨道坐标: left={track_left}, right={track_right}, y={track_y}")
                                
                                # 直接点击最右边
                                await self.page.mouse.click(track_right, track_y)
                                await asyncio.sleep(1)
                                
                                print("✅ 已直接点击最右边")
                        else:
                            print("❌ 未找到滑块容器")
                    
                    except Exception as e2:
                        print(f"备用方法失败: {e2}")
                
                # 验证滑块值是否已更改
                try:
                    await asyncio.sleep(1)  # 等待值更新
                    new_value = await quality_slider_element.get_attribute("value")
                    new_aria_valuenow = await quality_slider_element.get_attribute("aria-valuenow")
                    print(f"操作后滑块值: value={new_value}, aria-valuenow={new_aria_valuenow}")
                    
                    # 检查是否成功设置为最大值
                    if new_value == max_value or new_aria_valuenow == max_value or\
                       float(new_value or 0) > 0.95 or float(new_aria_valuenow or 0) > 0.95:
                        print("✅ 滑块已成功设置到Ultra位置")
                    else:
                        print(f"⚠️ 滑块可能未设置到Ultra位置，当前值: {new_value or new_aria_valuenow}")
                        
                        # 最后尝试: 直接使用键盘操作
                        try:
                            print("最后尝试: 使用键盘End键...")
                            await quality_slider_element.focus()
                            await asyncio.sleep(0.5)
                            await self.page.keyboard.press('End')
                            await asyncio.sleep(1)
                            
                            final_value = await quality_slider_element.get_attribute("value")
                            print(f"最终滑块值: {final_value}")
                        except Exception as e3:
                            print(f"键盘操作失败: {e3}")
                except Exception as e:
                    print(f"验证滑块值时出错: {e}")
                
                return True
            else:
                print("❌ 未找到Generation quality slider")
                
                # 尝试寻找页面中所有的滑块元素
                try:
                    all_sliders = await self.page.query_selector_all('input[type="range"], .MuiSlider-root')
                    print(f"页面中找到 {len(all_sliders)} 个滑块元素")
                    
                    for i, elem in enumerate(all_sliders[:5]):  # 只显示前5个
                        tag_name = await elem.evaluate('el => el.tagName')
                        class_name = await elem.get_attribute('class')
                        value = await elem.get_attribute('value')
                        min_attr = await elem.get_attribute('min')
                        max_attr = await elem.get_attribute('max')
                        print(f"滑块 {i}: <{tag_name}> class='{class_name}' value='{value}' min='{min_attr}' max='{max_attr}'")
                except Exception as e:
                    print(f"查找滑块元素时出错: {e}")
                
                return False
        
        except Exception as e:
            print(f"调整Generation quality slider时出错: {e}")
            return False
    
    async def select_length(self):
        """点击Length下拉框并选择2:10选项"""
        try:
            print("\n=== 点击Length下拉框选择2:10 ===")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 寻找Length下拉框
            length_combobox = None
            combobox_selectors = [
                'button[role="combobox"]:has-text("Length:")',
                'button[id="model-length"]',
                'button[role="combobox"][aria-controls*="radix"]',
                '.flex.items-start button[role="combobox"]',
                'button:has(span:has-text("Length:"))',
                'button[class*="cursor-pointer"]:has(svg.lucide-chevron-down)'
            ]
            
            for selector in combobox_selectors:
                try:
                    length_combobox = await self.page.wait_for_selector(selector, timeout=3000)
                    if length_combobox:
                        print(f"✅ 找到Length下拉框: {selector}")
                        break
                except Exception:
                    continue
            
            if length_combobox:
                # 检查当前展开状态
                aria_expanded = await length_combobox.get_attribute("aria-expanded")
                print(f"Length下拉框当前展开状态: {aria_expanded}")
                
                if aria_expanded == "false":
                    print("正在点击Length下拉框展开...")
                    await length_combobox.click()
                    print("✅ 已点击Length下拉框")
                    
                    # 等待下拉菜单显示
                    await asyncio.sleep(1)
                    
                    # 验证状态是否已更改
                    new_aria_expanded = await length_combobox.get_attribute("aria-expanded")
                    print(f"点击后的展开状态: {new_aria_expanded}")
                    
                    # 寻找并选择2:10选项（第二个元素）
                    if new_aria_expanded == "true":
                        print("下拉框已展开，正在寻找2:10选项...")
                        
                        # 使用多种选择器定位下拉选项
                        option_selectors = [
                            'div[role="listbox"] div[role="option"]:nth-child(2)',
                            'div[role="option"]:has-text("2:10")',
                            '[data-radix-collection-item]:nth-child(2)',
                            '[data-value="2:10"]',
                            '[class*="dropdown"] [role="option"]:nth-child(2)',
                            '[id*="radix-"] [role="option"]:nth-child(2)'
                        ]
                        
                        option_element = None
                        for selector in option_selectors:
                            try:
                                option_element = await self.page.wait_for_selector(selector, timeout=3000)
                                if option_element:
                                    # 确认找到的是2:10选项
                                    option_text = await option_element.inner_text()
                                    print(f"找到选项: '{option_text}'")
                                    
                                    if "2:10" in option_text:
                                        print(f"✅ 确认找到2:10选项: {selector}")
                                        break
                                    else:
                                        print(f"⚠️ 找到的选项不是2:10: '{option_text}'")
                                        option_element = None
                            except Exception:
                                continue
                        
                        # 点击2:10选项
                        if option_element:
                            print("正在点击2:10选项...")
                            await option_element.click()
                            await asyncio.sleep(1)
                            print("✅ 已点击2:10选项")
                            
                            # 验证选项是否生效
                            current_length_text = await length_combobox.inner_text()
                            print(f"当前显示的长度值: '{current_length_text}'")
                            
                            if "2:10" in current_length_text:
                                print("✅ 成功选择2:10选项")
                            else:
                                print(f"⚠️ 可能未成功选择2:10选项，当前显示: '{current_length_text}'")
                                
                                # 尝试使用JavaScript直接设置值
                                try:
                                    print("尝试使用JavaScript设置值...")
                                    await length_combobox.evaluate('''
                                        el => {
                                            // 查找并更新显示的文本
                                            const spans = el.querySelectorAll('span');
                                            for (const span of spans) {
                                                if (!span.textContent.includes('Length:')) {
                                                    span.textContent = '2:10';
                                                }
                                            }
                                            
                                            // 更新属性
                                            el.setAttribute('data-value', '2:10');
                                            
                                            // 关闭下拉框
                                            el.setAttribute('aria-expanded', 'false');
                                            el.setAttribute('data-state', 'closed');
                                            
                                            // 尝试触发change事件
                                            const changeEvent = new Event('change', { bubbles: true });
                                            el.dispatchEvent(changeEvent);
                                        }
                                    ''')
                                    await asyncio.sleep(1)
                                    print("✅ JavaScript设置尝试完成")
                                except Exception as e:
                                    print(f"JavaScript设置失败: {e}")
                        else:
                            print("❌ 未找到2:10选项")
                            
                            # 打印可见的所有选项
                            try:
                                all_options = await self.page.query_selector_all('div[role="option"], [data-radix-collection-item], [class*="dropdown"] > div')
                                print(f"下拉框中找到 {len(all_options)} 个选项")
                                
                                for i, elem in enumerate(all_options[:10]):
                                    option_text = await elem.inner_text()
                                    role_attr = await elem.get_attribute('role')
                                    class_name = await elem.get_attribute('class')
                                    print(f"选项 {i}: role='{role_attr}' class='{class_name}' text='{option_text}'")
                            except Exception as e:
                                print(f"查找选项时发生错误: {e}")
                    else:
                        print("⚠️ 点击后下拉框可能未成功展开")
                else:
                    print("Length下拉框已经是展开状态，继续选择2:10选项...")
                    # 直接寻找并选择2:10选项，逻辑与上面相同...
                
                return True
            else:
                print("❌ 未找到Length下拉框")
                
                # 打印页面中所有可能的combobox
                try:
                    all_comboboxes = await self.page.query_selector_all('button[role="combobox"], [aria-autocomplete="none"], [class*="dropdown"]')
                    print(f"页面中找到 {len(all_comboboxes)} 个可能的下拉框")
                    
                    for i, elem in enumerate(all_comboboxes[:10]):
                        tag_name = await elem.evaluate('el => el.tagName')
                        class_name = await elem.get_attribute('class')
                        role_attr = await elem.get_attribute('role')
                        inner_text = await elem.inner_text()
                        print(f"下拉框 {i}: <{tag_name}> role='{role_attr}' class='{class_name}' text='{inner_text[:50]}'")
                except Exception as e:
                    print(f"查找下拉框时发生错误: {e}")
                
                return False
                
        except Exception as e:
            print(f"选择Length选项时发生错误: {e}")
            return False
        
    async def click_advanced_controls(self):
        """点击Advanced Controls按钮使其展开"""
        try:
            print("\n=== 点击Advanced Controls按钮 ===")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 尝试多种选择器找到Advanced Controls按钮
            advanced_controls_button = None
            button_selectors = [
                'button[aria-controls*="radix"][aria-expanded="false"]:has-text("Advanced Controls")',
                'button:has-text("Advanced Controls")',
                '.flex.items-center button:has(.text-muted-foreground)',
                'h3:has-text("Advanced Controls")',
                'button:has(h3:has-text("Advanced Controls"))',
                'button[class*="flex"]:has(svg.lucide-chevron-down)'
            ]
            
            for selector in button_selectors:
                try:
                    advanced_controls_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if advanced_controls_button:
                        print(f"✅ 找到Advanced Controls按钮: {selector}")
                        break
                except Exception:
                    continue
            
            if advanced_controls_button:
                # 检查当前展开状态
                aria_expanded = await advanced_controls_button.get_attribute("aria-expanded")
                print(f"Advanced Controls按钮当前展开状态: {aria_expanded}")
                
                if aria_expanded == "false":
                    print("正在点击Advanced Controls按钮展开...")
                    await advanced_controls_button.click()
                    print("✅ 已点击Advanced Controls按钮")
                    
                    # 等待状态更新
                    await asyncio.sleep(1)
                    
                    # 验证状态是否已更改
                    new_aria_expanded = await advanced_controls_button.get_attribute("aria-expanded")
                    print(f"点击后的展开状态: {new_aria_expanded}")
                    
                    if new_aria_expanded == "true":
                        print("✅ Advanced Controls已成功展开")
                    else:
                        print("⚠️ Advanced Controls可能未成功展开")
                        
                        # 尝试使用JavaScript强制展开
                        try:
                            print("尝试使用JavaScript强制展开...")
                            await advanced_controls_button.evaluate('''
                                el => {
                                    // 设置属性
                                    el.setAttribute("aria-expanded", "true");
                                    el.setAttribute("data-state", "open");
                                    
                                    // 触发点击事件
                                    el.click();
                                    
                                    // 查找并展开对应的内容区域
                                    const controlsId = el.getAttribute("aria-controls");
                                    if (controlsId) {
                                        const contentArea = document.getElementById(controlsId);
                                        if (contentArea) {
                                            contentArea.style.display = "block";
                                            contentArea.setAttribute("data-state", "open");
                                        }
                                    }
                                    
                                    // 查找SVG图标并旋转
                                    const svg = el.querySelector('svg');
                                    if (svg) {
                                        svg.style.transform = 'rotate(180deg)';
                                    }
                                }
                            ''')
                            await asyncio.sleep(1)
                            print("✅ JavaScript强制展开尝试完成")
                        except Exception as e:
                            print(f"JavaScript强制展开失败: {e}")
                else:
                    print("Advanced Controls按钮已经是展开状态，无需点击")
                
                return True
            else:
                print("❌ 未找到Advanced Controls按钮")
                
                # 打印页面中所有可能的按钮
                try:
                    all_buttons = await self.page.query_selector_all('button')
                    print(f"页面中找到 {len(all_buttons)} 个按钮")
                    
                    for i, elem in enumerate(all_buttons[:10]):  # 只显示前10个
                        tag_name = await elem.evaluate('el => el.tagName')
                        class_name = await elem.get_attribute('class')
                        aria_expanded = await elem.get_attribute('aria-expanded')
                        inner_text = await elem.inner_text()
                        print(f"按钮 {i}: <{tag_name}> class='{class_name}' aria-expanded='{aria_expanded}' text='{inner_text[:50]}'")
                except Exception as e:
                    print(f"查找按钮时发生错误: {e}")
                
                return False
                
        except Exception as e:
            print(f"点击Advanced Controls按钮时发生错误: {e}")
            return False
    
    async def wait_and_close(self):
        """等待用户确认后关闭"""
        print("\n=== 所有操作完成 ===")
        print("请在浏览器中检查操作结果...")
        print("检查完成后请在此终端按 Enter 键关闭浏览器...")
        input()
        
        await self.close()
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.context:
                await self.context.close()
                print("浏览器上下文已关闭")
            
            if self.playwright:
                await self.playwright.stop()
                print("Playwright已停止")
                
        except Exception as e:
            print(f"关闭时发生错误: {e}")

async def main():
    # 设置音频文件目录
    audio_folder = r"C:\Users\<USER>\Documents\AudFree Apple Music Converter\Output\BEATstrumentals"
    # 记录已处理文件的JSON路径
    processed_json = os.path.join(audio_folder, "processed_files.json")
    
    # 检查音频目录是否存在
    if not os.path.exists(audio_folder):
        print(f"❌ 音频文件目录不存在: {audio_folder}")
        return
    
    print(f"✅ 找到音频文件目录: {audio_folder}")
    
    # 获取所有m4a文件
    audio_files = glob.glob(os.path.join(audio_folder, "*.m4a"))
    if not audio_files:
        print("❌ 目录中没有找到m4a文件")
        return
    
    print(f"✅ 共找到 {len(audio_files)} 个m4a文件")
    
    # 加载或创建已处理文件记录
    processed_files = []
    if os.path.exists(processed_json):
        try:
            with open(processed_json, 'r', encoding='utf-8') as f:
                processed_files = json.load(f)
            print(f"✅ 已加载已处理文件记录，已处理 {len(processed_files)} 个文件")
        except Exception as e:
            print(f"⚠️ 加载已处理文件记录时出错: {e}")
            processed_files = []
    else:
        print("✅ 创建新的已处理文件记录")
    
    # 过滤未处理的文件
    files_to_process = [f for f in audio_files if os.path.basename(f) not in processed_files]
    if not files_to_process:
        print("✅ 所有文件已处理完成，无需再次处理")
        return
    
    print(f"✅ 还有 {len(files_to_process)} 个文件需要处理")
    for i, file in enumerate(files_to_process):
        print(f"  {i+1}. {os.path.basename(file)}")
    
    # 检查browser_data目录
    if os.path.exists("./browser_data"):
        print("✅ 找到 browser_data 目录，将使用保存的登录状态")
    else:
        print("⚠️ 未找到 browser_data 目录，可能需要重新登录")
    
    uploader = AudioUploader()  # 创建上传器实例
    
    try:
        # 启动浏览器
        await uploader.start_browser()
        
        # 依次处理每个文件
        file_counter = 0  # 计数器，用于跟踪已处理文件数量
        
        for file_index, audio_file in enumerate(files_to_process):
            # 判断是否需要等待
            if file_index > 0:  # 如果不是第一个文件，需要等徆60秒
                print(f"\n=== 已处理 {file_counter} 个文件，等待 60 秒后处理下一个文件 ===\n")
                await asyncio.sleep(60)  # 等待 60 秒
                
                # 点击Reset按钮重置页面
                reset_success = await uploader.click_reset_button()
                if not reset_success:
                    print(f"\n\u26a0\ufe0f 重置页面失败，跳过文件: {os.path.basename(audio_file)}\n")
                    continue  # 跳过这个文件
                
                # 等待重置完成
                await asyncio.sleep(2)
            
            # 当前文件信息
            file_name = os.path.basename(audio_file)
            file_size = os.path.getsize(audio_file) / (1024*1024)
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n=== [{file_index+1}/{len(files_to_process)}] 开始处理文件: {file_name} (大小: {file_size:.2f} MB) 时间: {current_time} ===\n")
            
            # 上传音频文件
            upload_success = await uploader.upload_audio_file(audio_file)
            if upload_success:
                print(f"🎉 文件 {file_name} 上传成功！")
                
                # 处理上传后的页面元素
                understand_success = await uploader.handle_understand_button_and_style()
                if not understand_success:
                    print(f"❌ 文件 {file_name} 处理上传后的UI元素失败")
                    continue
                
                print(f"✅ 文件 {file_name} 成功处理了所有UI元素")
                
                # 选择音频长度
                length_success = await uploader.select_length()
                if not length_success:
                    print(f"❌ 文件 {file_name} 选择音频长度失败")
                    continue
                
                print(f"✅ 文件 {file_name} 成功选择音频长度")
                
                # 调整生成质量滑块
                quality_success = await uploader.adjust_generation_quality_slider()
                if not quality_success:
                    print(f"❌ 文件 {file_name} 调整生成质量滑块失败")
                    continue
                
                print(f"✅ 文件 {file_name} 成功调整生成质量滑块")
                
                # 点击Create按钮
                create_success = await uploader.click_create_button()
                if not create_success:
                    print(f"❌ 文件 {file_name} 点击Create按钮失败")
                    continue
                
                print(f"✅ 文件 {file_name} 成功点击Create按钮，正在生成音频")
                
                # 记录已处理的文件
                processed_files.append(file_name)
                try:
                    with open(processed_json, 'w', encoding='utf-8') as f:
                        json.dump(processed_files, f, ensure_ascii=False, indent=2)
                    print(f"✅ 已将 {file_name} 添加到已处理文件记录")
                except Exception as e:
                    print(f"⚠️ 保存已处理文件记录时出错: {e}")
                
                # 更新计数器
                file_counter += 1
                
            else:
                print(f"❌ 文件 {file_name} 上传失败")
        
        print(f"\n🎉 批处理完成！共成功处理 {file_counter}/{len(files_to_process)} 个文件\n")
        print("处理结束，请在浏览器中确认结果...")
        input("按Enter键关闭浏览器并退出程序...")
    
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        traceback.print_exc()
    finally:
        # 确保浏览器被关闭
        await uploader.close()

if __name__ == "__main__":
    asyncio.run(main())